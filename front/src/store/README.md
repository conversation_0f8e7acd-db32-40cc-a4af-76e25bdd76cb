# Store 使用说明

## UserStore 用户状态管理

### 导入方式
```javascript
import { useUserStore } from '@/store'
// 或者
import { useUserStore } from '@/store/modules/user'
```

### 基本使用

#### 在组件中使用
```javascript
<script setup>
import { useUserStore } from '@/store'

const userStore = useUserStore()

// 获取状态（响应式ref）
console.log(userStore.id)
console.log(userStore.username)
console.log(userStore.token)

// 使用计算属性（响应式computed）
console.log(userStore.isLoggedIn)
console.log(userStore.userInfo)
console.log(userStore.authHeader)

// 在模板中使用
</script>

<template>
  <div v-if="userStore.isLoggedIn">
    <h1>欢迎, {{ userStore.username }}!</h1>
    <p>用户ID: {{ userStore.id }}</p>
  </div>
  <div v-else>
    <p>请先登录</p>
  </div>
</template>
```

### 主要方法

#### 1. 登录
```javascript
const userStore = useUserStore()

// 登录成功后调用
userStore.login({
  id: 123,
  username: 'zhangsan',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
})
```

#### 2. 登出
```javascript
const userStore = useUserStore()

// 登出
userStore.logout()
```

#### 3. 更新用户信息
```javascript
const userStore = useUserStore()

// 更新用户名
userStore.updateUsername('新用户名')

// 设置token
userStore.setToken('new-token')

// 设置完整用户信息
userStore.setUserInfo({
  id: 456,
  username: 'lisi',
  token: 'new-token'
})
```

#### 4. 响应式特性
```javascript
import { watch } from 'vue'
const userStore = useUserStore()

// 监听登录状态变化
watch(userStore.isLoggedIn, (newValue) => {
  if (newValue) {
    console.log('用户已登录')
  } else {
    console.log('用户已登出')
  }
})

// 监听用户名变化
watch(() => userStore.username, (newUsername) => {
  console.log('用户名已更新:', newUsername)
})
```

### 状态字段（响应式ref）

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Ref<number/null> | 用户ID |
| username | Ref<string> | 用户名 |
| token | Ref<string> | 认证token |

### 计算属性（响应式computed）

| 属性 | 类型 | 说明 |
|------|------|------|
| isLoggedIn | ComputedRef<boolean> | 是否已登录 |
| userInfo | ComputedRef<object> | 用户信息对象 |
| authHeader | ComputedRef<string> | 认证头（Bearer token） |

### 持久化

用户数据会自动持久化到localStorage中，key为 `mailcode-user`，页面刷新后数据不会丢失。

### 在API请求中使用

```javascript
import { useUserStore } from '@/store'
import { api } from '@/utils/request'

const userStore = useUserStore()

// 检查登录状态
if (!userStore.isLoggedIn) {
  // 跳转到登录页
  router.push('/login')
  return
}

// 使用token进行API请求（request.js会自动从localStorage获取token）
const result = await api.get('/user/profile')
```
