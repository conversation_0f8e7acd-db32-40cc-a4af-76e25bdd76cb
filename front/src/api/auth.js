import { api } from '@/utils/request'

/**
 * 认证相关基础API
 */

// 用户注册
export const register = (data) => {
  return api.post('/api/auth/register', {
    username: data.username,
    email: data.email,
    password: data.password,
    mp_open_id: data.mp_open_id || null
  })
}

// 用户登录
export const login = (data) => {
  return api.post('/api/auth/login', {
    email: data.email,
    password: data.password
  })
}

// 获取当前用户信息（包含角色和菜单）
export const getUserInfo = () => {
  return api.get('/api/auth/me')
}

// 获取当前用户信息
export const getUserProfile = () => {
  return api.get('/api/auth/profile')
}

// 修改密码
export const changePassword = (data) => {
  return api.put('/api/auth/password', {
    oldPassword: data.oldPassword,
    newPassword: data.newPassword
  })
}

// 用户登出
export const logout = () => {
  return api.post('/api/auth/logout')
}
