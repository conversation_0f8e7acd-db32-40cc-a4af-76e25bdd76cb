import axios from 'axios'
import { MessagePlugin } from 'tdesign-vue-next'
import { useUserStore } from '@/store'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/dev-api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 从userStore获取token
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    console.log('请求发送:', config)
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    console.log('响应接收:', response)

    const { data } = response

    // 统一处理响应数据格式
    if (data.code === 200 || data.success === true) {
      return data
    } else {
      // 业务错误处理
      const message = data.message || '请求失败'
      MessagePlugin.error(message)
      return Promise.reject(new Error(message))
    }
  },
  (error) => {
    console.error('响应错误:', error)

    // 网络错误处理
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          MessagePlugin.error('登录已过期，请重新登录')
          // 清除用户信息
          const userStore = useUserStore()
          userStore.logout()
          // 跳转到登录页
          window.location.href = '/login'
          break
        case 403:
          MessagePlugin.error('没有权限访问')
          break
        case 404:
          MessagePlugin.error('请求的资源不存在')
          break
        case 500:
          MessagePlugin.error('服务器内部错误')
          break
        default:
          MessagePlugin.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.code === 'ECONNABORTED') {
      MessagePlugin.error('请求超时，请稍后重试')
    } else {
      MessagePlugin.error('网络连接失败，请检查网络')
    }

    return Promise.reject(error)
  }
)

// 封装常用的请求方法
export const api = {
  // GET请求 - 使用params传递查询参数
  get(url, params = {}, config = {}) {
    return request({
      method: 'get',
      url,
      params, // 查询参数，会拼接到URL后面 ?key=value
      ...config
    })
  },

  // POST请求 - 使用data传递请求体数据
  post(url, data = {}, params = {}, config = {}) {
    return request({
      method: 'post',
      url,
      data, // 请求体数据
      params, // 查询参数（可选）
      ...config
    })
  },

  // PUT请求 - 使用data传递请求体数据
  put(url, data = {}, params = {}, config = {}) {
    return request({
      method: 'put',
      url,
      data, // 请求体数据
      params, // 查询参数（可选）
      ...config
    })
  },

  // DELETE请求 - 可以使用params传递查询参数
  delete(url, params = {}, config = {}) {
    return request({
      method: 'delete',
      url,
      params, // 查询参数
      ...config
    })
  },

  // PATCH请求 - 使用data传递请求体数据
  patch(url, data = {}, params = {}, config = {}) {
    return request({
      method: 'patch',
      url,
      data, // 请求体数据
      params, // 查询参数（可选）
      ...config
    })
  },

  // 文件上传
  upload(url, formData, config = {}) {
    return request({
      method: 'post',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    })
  },

  // 文件下载
  download(url, params = {}, filename = '') {
    return request({
      method: 'get',
      url,
      params,
      responseType: 'blob'
    }).then(response => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
  }
}

// 导出axios实例（用于特殊需求）
export default request

/**
 * API使用说明：
 *
 * 1. GET请求 - 查询数据
 *    api.get('/users', { page: 1, size: 10 })
 *    // 生成URL: /users?page=1&size=10
 *
 * 2. POST请求 - 创建数据
 *    api.post('/users', { name: '张三', email: '<EMAIL>' })
 *    // 请求体: { name: '张三', email: '<EMAIL>' }
 *
 *    // 带查询参数的POST
 *    api.post('/users', { name: '张三' }, { source: 'admin' })
 *    // URL: /users?source=admin, 请求体: { name: '张三' }
 *
 * 3. PUT请求 - 更新数据
 *    api.put('/users/123', { name: '李四' })
 *    // 请求体: { name: '李四' }
 *
 * 4. DELETE请求 - 删除数据
 *    api.delete('/users/123')
 *    api.delete('/users', { ids: [1, 2, 3] })
 *    // URL: /users?ids=1,2,3
 *
 * 5. PATCH请求 - 部分更新
 *    api.patch('/users/123', { status: 'active' })
 *    // 请求体: { status: 'active' }
 *
 * 6. 文件上传
 *    const formData = new FormData()
 *    formData.append('file', file)
 *    api.upload('/upload', formData)
 *
 * 7. 文件下载
 *    api.download('/export', { type: 'excel' }, 'users.xlsx')
 */
