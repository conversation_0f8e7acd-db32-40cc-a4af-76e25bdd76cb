import { createRouter, createWebHistory } from 'vue-router';

// 基础路由配置
const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home/index.vue')
  },
  {
    path: '/get-mailbox',
    name: 'GetMailbox',
    component: () => import('@/views/GetMailbox/index.vue')
  },
  {
    path: '/my-mailbox',
    name: 'MyMailbox',
    component: () => import('@/views/MyMailbox/index.vue')
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login/index.vue')
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/Register/index.vue')
  },
  {
    path: '/contact',
    name: 'Contact',
    component: () => import('@/views/Contact/index.vue')
  }
];

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_URL),
  routes,
  scrollBehavior() {
    return {
      el: '#app',
      top: 0,
      behavior: 'smooth',
    };
  },
});

export default router;
