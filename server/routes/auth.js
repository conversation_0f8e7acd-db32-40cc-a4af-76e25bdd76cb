const express = require('express');
const User = require('../models/User');
const Menu = require('../models/Menu');
const { generateToken } = require('../utils/jwt');
const { authMiddleware } = require('../middleware/auth');
const { query } = require('../config/database');
const redisUtils = require('../utils/redisUtils');
const router = express.Router();

// 用户注册
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, mp_open_id } = req.body;
    
    // 简单验证
    if (!username || !email || !password) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '用户名、邮箱和密码都是必填项'
      });
    }

    // 检查邮箱是否已存在
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '邮箱已被使用'
      });
    }

    // 创建用户
    const user = await User.create({ username, email, password, mp_open_id });

    // 查询普通用户角色
    const roleSql = 'SELECT id FROM role WHERE code = ? AND status = 1';
    const roles = await query(roleSql, ['user']);

    if (roles.length > 0) {
      // 分配默认角色（普通用户）
      const { v4: uuidv4 } = require('uuid');
      const defaultRoleId = roles[0].id;
      const userRoleId = uuidv4();

      await query(
        'INSERT INTO user_role (id, user_id, role_id) VALUES (?, ?, ?)',
        [userRoleId, user.id, defaultRoleId]
      );
    }

    // 生成JWT token（只包含用户ID，其他信息从Redis获取）
    const token = generateToken(user.id);

    // 将token存储到Redis（token为键，用户ID为值），过期时间5小时
    await redisUtils.setToken(token, user.id, 18000);

    res.status(200).json({
      code: 200,
      data: {
        token
      },
      message: '注册成功'
    });
  } catch (error) {
    console.error('注册失败:', error.message);
    res.status(500).json({
      code: 500,
      data: null,
      message: '注册失败'
    });
  }
});

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // 验证必填字段
    if (!email || !password) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '邮箱和密码都是必填项'
      });
    }

    // 验证用户登录
    const user = await User.validateLogin(email, password);
    if (!user) {
      return res.status(401).json({
        code: 401,
        data: null,
        message: '邮箱或密码错误'
      });
    }

    // 生成JWT token（只包含用户ID，其他信息从Redis获取）
    const token = generateToken(user.id);

    // 将token存储到Redis（token为键，用户ID为值），过期时间5小时
    await redisUtils.setToken(token, user.id, 18000);

    res.json({
      code: 200,
      data: {
        token
      },
      message: '登录成功'
    });
  } catch (error) {
    console.error('登录失败:', error.message);
    res.status(500).json({
      code: 500,
      data: null,
      message: '登录失败'
    });
  }
});

// 获取用户完整信息（合并profile和me方法）
router.get('/getUserInfo', authMiddleware, async (req, res) => {
  try {
    const userId = req.user.id;

    // 获取用户基本信息
    const user = {
      id: req.user.id,
      username: req.user.username,
      email: req.user.email,
      mp_open_id: req.user.mp_open_id,
      create_time: req.user.create_time,
      update_time: req.user.update_time,
      status: req.user.status
    };

    // 获取用户角色（从中间件中获取，包含权限信息）
    const roles = req.user.roles || [];

    // 获取用户权限（从中间件中获取）
    const permissions = req.user.permissions || [];

    // 获取用户菜单
    const menus = await Menu.findByUserId(userId);

    res.json({
      code: 200,
      data: {
        user: user,
        roles: roles,
        permissions: permissions,
        menus: menus
      },
      message: '获取用户信息成功'
    });
  } catch (error) {
    console.error('获取用户信息失败:', error.message);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取用户信息失败'
    });
  }
});

// 修改密码
router.put('/password', authMiddleware, async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;
    
    if (!oldPassword || !newPassword) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '旧密码和新密码都是必填项'
      });
    }

    // 验证旧密码
    const user = await User.validateLogin(req.user.email, oldPassword);
    if (!user) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '旧密码错误'
      });
    }

    // 更新密码
    const success = await User.updatePassword(req.user.id, newPassword);
    if (!success) {
      return res.status(500).json({
        code: 500,
        data: null,
        message: '密码更新失败'
      });
    }

    res.json({
      code: 200,
      data: null,
      message: '密码修改成功'
    });
  } catch (error) {
    console.error('修改密码失败:', error.message);
    res.status(500).json({
      code: 500,
      data: null,
      message: '修改密码失败'
    });
  }
});

// 用户登出
router.post('/logout', authMiddleware, async (req, res) => {
  try {
    const token = req.headers.authorization?.substring(7); // 移除 "Bearer " 前缀

    if (token) {
      // 从Redis中删除token
      const deleted = await redisUtils.deleteToken(token);

      if (deleted) {
        console.log(`用户登出成功，token已从Redis中删除`);
      } else {
        console.log(`用户登出，但Redis中未找到对应token`);
      }
    }

    res.json({
      code: 200,
      data: null,
      message: '登出成功'
    });
  } catch (error) {
    console.error('登出失败:', error.message);
    res.status(500).json({
      code: 500,
      data: null,
      message: '登出失败'
    });
  }
});



module.exports = router;
