const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { initDatabase, testConnection } = require('./config/database');
const { redisClient } = require('./config/redis');
const userRoutes = require('./routes/users');
const authRoutes = require('./routes/auth');
const menuRoutes = require('./routes/menus');
const roleRoutes = require('./routes/roles');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 9090;

// 中间件
app.use(helmet()); // 安全中间件
app.use(cors()); // 跨域中间件
app.use(morgan('combined')); // 日志中间件
app.use(express.json()); // 解析JSON请求体
app.use(express.urlencoded({ extended: true })); // 解析URL编码请求体

// 路由
app.get('/', async (req, res) => {
  try {
    const dbStatus = await testConnection();
    const redisStatus = await redisClient.testConnection();

    res.json({
      code: 200,
      data: {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        services: {
          mysql: {
            status: dbStatus ? 'connected' : 'disconnected',
            message: dbStatus ? 'MySQL连接正常' : 'MySQL连接异常'
          },
          redis: {
            status: redisStatus ? 'connected' : 'disconnected',
            message: redisStatus ? 'Redis连接正常' : 'Redis连接异常'
          }
        }
      },
      message: 'MailCode API服务器运行中'
    });
  } catch (error) {
    console.error('获取服务状态失败:', error.message);
    res.status(500).json({
      code: 500,
      data: {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        services: {
          mysql: {
            status: 'error',
            message: 'MySQL状态检查失败'
          },
          redis: {
            status: 'error',
            message: 'Redis状态检查失败'
          }
        }
      },
      message: 'MailCode API服务器运行中，但服务状态检查异常'
    });
  }
});

// API路由
app.get('/api/health', async (req, res) => {
  try {
    const dbStatus = await testConnection();
    const redisStatus = await redisClient.testConnection();
    res.json({
      code: 200,
      data: {
        status: 'OK',
        database: dbStatus ? '连接正常' : '连接异常',
        redis: redisStatus ? '连接正常' : '连接异常',
        timestamp: new Date().toISOString()
      },
      message: '服务器健康状态良好'
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      data: {
        status: 'ERROR',
        database: '连接异常',
        redis: '连接异常',
        timestamp: new Date().toISOString()
      },
      message: '服务器健康检查失败'
    });
  }
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/menus', menuRoutes);
app.use('/api/roles', roleRoutes);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    data: null,
    message: `路径 ${req.originalUrl} 不存在`
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    code: 500,
    data: null,
    message: process.env.NODE_ENV === 'development' ? err.message : '服务器出现问题'
  });
});

// 启动服务器
async function startServer() {
  console.log('🚀 开始启动MailCode服务器...');

  try {
    // 第一步：初始化数据库连接
    console.log('📊 正在连接MySQL数据库...');
    await initDatabase();

    // 验证数据库连接
    const dbConnected = await testConnection();
    if (!dbConnected) {
      throw new Error('MySQL数据库连接验证失败');
    }
    console.log('✅ MySQL数据库连接成功');

    // 第二步：初始化Redis连接
    console.log('🔴 正在连接Redis服务器...');
    const redisConnected = await redisClient.connect();
    if (!redisConnected) {
      throw new Error('Redis连接失败');
    }

    // 验证Redis连接
    const redisStatus = await redisClient.testConnection();
    if (!redisStatus) {
      throw new Error('Redis连接验证失败');
    }
    console.log('✅ Redis连接成功');

    // 第三步：启动HTTP服务器
    console.log('🌐 正在启动HTTP服务器...');
    app.listen(PORT, () => {
      console.log('\n🎉 MailCode服务器启动成功！');
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      console.log(`📍 服务器地址: http://localhost:${PORT}`);
      console.log(`🔗 健康检查: http://localhost:${PORT}/api/health`);
      console.log(`📋 API文档: http://localhost:${PORT}/`);
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      console.log('📊 MySQL: 连接正常');
      console.log('🔴 Redis: 连接正常');
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      console.log('✨ 服务器已就绪，等待请求...\n');
    });

  } catch (error) {
    console.error('\n❌ 服务器启动失败:');
    console.error('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.error(`💥 错误信息: ${error.message}`);
    console.error('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // 检查具体的连接状态
    console.log('\n🔍 正在检查服务状态...');

    try {
      const dbStatus = await testConnection();
      console.log(`📊 MySQL状态: ${dbStatus ? '✅ 正常' : '❌ 异常'}`);
    } catch (dbError) {
      console.log('📊 MySQL状态: ❌ 连接失败');
    }

    try {
      const redisStatus = await redisClient.testConnection();
      console.log(`🔴 Redis状态: ${redisStatus ? '✅ 正常' : '❌ 异常'}`);
    } catch (redisError) {
      console.log('🔴 Redis状态: ❌ 连接失败');
    }

    console.log('\n💡 请检查以下项目:');
    console.log('   1. MySQL服务是否启动 (端口3306)');
    console.log('   2. Redis服务是否启动 (端口6379)');
    console.log('   3. 数据库配置是否正确');
    console.log('   4. Redis密码是否正确 (123456)');
    console.log('   5. 网络连接是否正常\n');

    // 清理资源
    try {
      await redisClient.disconnect();
    } catch (cleanupError) {
      // 忽略清理错误
    }

    console.log('🛑 服务器启动终止\n');
    process.exit(1);
  }
}

// 优雅关闭处理
process.on('SIGINT', async () => {
  console.log('\n🛑 接收到关闭信号，正在优雅关闭服务器...');

  try {
    // 关闭Redis连接
    await redisClient.disconnect();
    console.log('✅ Redis连接已关闭');

    console.log('👋 服务器已安全关闭');
    process.exit(0);
  } catch (error) {
    console.error('❌ 关闭过程中出现错误:', error.message);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 接收到终止信号，正在关闭服务器...');

  try {
    await redisClient.disconnect();
    console.log('✅ Redis连接已关闭');
    process.exit(0);
  } catch (error) {
    console.error('❌ 关闭过程中出现错误:', error.message);
    process.exit(1);
  }
});

// 启动服务器
startServer();

module.exports = app;
