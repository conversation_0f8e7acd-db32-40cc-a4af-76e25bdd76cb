const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { initDatabase, testConnection } = require('./config/database');
const { redisClient } = require('./config/redis');
const userRoutes = require('./routes/users');
const authRoutes = require('./routes/auth');
const menuRoutes = require('./routes/menus');
const roleRoutes = require('./routes/roles');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 9090;

// 中间件
app.use(helmet()); // 安全中间件
app.use(cors()); // 跨域中间件
app.use(morgan('combined')); // 日志中间件
app.use(express.json()); // 解析JSON请求体
app.use(express.urlencoded({ extended: true })); // 解析URL编码请求体

// 路由
app.get('/', (req, res) => {
  res.json({
    code: 200,
    data: {
      version: '1.0.0',
      timestamp: new Date().toISOString()
    },
    message: 'MailCode API服务器运行中'
  });
});

// API路由
app.get('/api/health', async (req, res) => {
  try {
    const dbStatus = await testConnection();
    const redisStatus = await redisClient.testConnection();
    res.json({
      code: 200,
      data: {
        status: 'OK',
        database: dbStatus ? '连接正常' : '连接异常',
        redis: redisStatus ? '连接正常' : '连接异常',
        timestamp: new Date().toISOString()
      },
      message: '服务器健康状态良好'
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      data: {
        status: 'ERROR',
        database: '连接异常',
        redis: '连接异常',
        timestamp: new Date().toISOString()
      },
      message: '服务器健康检查失败'
    });
  }
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/menus', menuRoutes);
app.use('/api/roles', roleRoutes);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    data: null,
    message: `路径 ${req.originalUrl} 不存在`
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    code: 500,
    data: null,
    message: process.env.NODE_ENV === 'development' ? err.message : '服务器出现问题'
  });
});

// 启动服务器
async function startServer() {
  try {
    // 初始化数据库
    await initDatabase();

    // 初始化Redis连接
    await redisClient.connect();

    // 启动服务器
    app.listen(PORT, () => {
      console.log(`服务器运行在端口 ${PORT}`);
      console.log(`访问地址: http://localhost:${PORT}`);
      console.log('数据库连接已建立');
      console.log('Redis连接已建立');
    });
  } catch (error) {
    console.error('服务器启动失败:', error.message);
    process.exit(1);
  }
}

startServer();

module.exports = app;
