const { verifyToken, extractTokenFromHeader } = require('../utils/jwt');
const { query } = require('../config/database');

/**
 * JWT认证中间件
 * 验证请求中的JWT token，并将用户信息添加到req.user中
 */
async function authMiddleware(req, res, next) {
  try {
    // 从请求头中提取token
    const token = extractTokenFromHeader(req);
    
    if (!token) {
      return res.status(401).json({
        code: 401,
        data: null,
        message: '未提供认证token'
      });
    }

    // 验证token
    const decoded = verifyToken(token);
    
    // 从数据库中获取用户信息（确保用户仍然存在且有效）
    const userSql = 'SELECT id, username, email, mp_open_id, create_time, update_time FROM user WHERE id = ?';
    const users = await query(userSql, [decoded.userId]);
    
    if (users.length === 0) {
      return res.status(401).json({
        code: 401,
        data: null,
        message: '用户不存在或已被删除'
      });
    }

    const user = users[0];

    // 获取用户角色和权限
    const rolesSql = `
      SELECT r.id, r.name, r.code, r.permissions 
      FROM user_role ur
      JOIN role r ON ur.role_id = r.id
      WHERE ur.user_id = ? AND r.status = 1
    `;
    const roles = await query(rolesSql, [user.id]);

    // 合并所有角色的权限
    const permissions = new Set();
    roles.forEach(role => {
      if (role.permissions) {
        JSON.parse(role.permissions).forEach(perm => permissions.add(perm));
      }
    });

    // 将用户信息和权限添加到请求对象中
    req.user = {
      ...user,
      roles: roles.map(role => ({
        id: role.id,
        name: role.name,
        code: role.code
      })),
      permissions: Array.from(permissions)
    };

    next();
  } catch (error) {
    console.error('认证中间件错误:', error.message);
    
    if (error.message.includes('Token已过期')) {
      return res.status(401).json({
        code: 401,
        data: null,
        message: 'Token已过期，请重新登录'
      });
    } else if (error.message.includes('Token无效')) {
      return res.status(401).json({
        code: 401,
        data: null,
        message: 'Token无效'
      });
    } else {
      return res.status(500).json({
        code: 500,
        data: null,
        message: '认证验证失败'
      });
    }
  }
}

/**
 * 可选认证中间件
 * 如果有token则验证，没有token则跳过（用于一些可选登录的接口）
 */
async function optionalAuthMiddleware(req, res, next) {
  try {
    const token = extractTokenFromHeader(req);
    
    if (!token) {
      // 没有token，设置为匿名用户
      req.user = null;
      return next();
    }

    // 有token，执行正常的认证流程
    await authMiddleware(req, res, next);
  } catch (error) {
    // 认证失败，设置为匿名用户
    req.user = null;
    next();
  }
}

module.exports = {
  authMiddleware,
  optionalAuthMiddleware
};
