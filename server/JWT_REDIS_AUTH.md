# JWT + Redis 认证机制说明

## 概述

本项目采用JWT + Redis的混合认证机制，充分发挥两者的优势：
- **JWT**: 无状态、自包含的token格式
- **Redis**: 集中式的会话管理和过期控制

## 设计原则

### 1. JWT简化设计
JWT中只包含最基本的信息，不存储敏感用户数据：

```json
{
  "userId": "user_123",
  "type": "access_token", 
  "iat": 1754120684,
  "exp": 1754207084,
  "iss": "mailcode-api",
  "sub": "user_123"
}
```

### 2. Redis控制过期
- **JWT过期时间**: 30天（很长，基本不会过期）
- **实际过期控制**: Redis中的token，5小时过期
- **优势**: 可以随时撤销token，实现真正的登出

### 3. 双重验证机制
1. 验证JWT的签名和格式
2. 检查Redis中是否存在对应的token记录

## 实现细节

### JWT生成
```javascript
// 只传入用户ID
const token = generateToken(userId);

// JWT内容简化
const payload = {
  userId: userId,
  type: 'access_token',
  iat: Math.floor(Date.now() / 1000)
};
```

### Redis存储结构
```
键: token:{jwt_token}
值: {
  "userId": "user_123",
  "loginTime": "2025-08-02T07:44:44.536Z",
  "expiresAt": "2025-08-02T12:44:44.536Z"
}
过期时间: 18000秒（5小时）
```

**设计优势**：
- 直接通过token查找用户ID，验证更高效
- 支持同一用户多个token并存（多设备登录）
- 可以精确删除特定token（单设备登出）
- 避免了用户ID到token的反向查找

### 认证流程

#### 登录/注册时
1. 生成JWT token（只包含用户ID）
2. 将token作为键，用户ID作为值存储到Redis，设置5小时过期
3. 返回token给客户端

#### 请求验证时
1. 从请求头提取JWT token
2. 验证JWT签名和格式
3. 使用token作为键从Redis获取用户ID
4. 验证JWT中的用户ID与Redis中的用户ID是否一致
5. 如果验证通过，刷新token过期时间（活跃用户延长会话）
6. 从数据库获取最新的用户信息
7. 检查用户状态（是否被禁用）

#### 登出时
1. 使用token作为键从Redis中删除对应记录
2. JWT虽然还在客户端，但无法通过Redis验证

## 安全优势

### 1. 可撤销性
- 传统JWT无法撤销，只能等待过期
- 现在可以通过删除Redis记录立即撤销token

### 2. 实时控制
- 用户被禁用时，立即清除Redis中的token
- 用户权限变更时，可以强制重新登录

### 3. 会话管理
- 可以查看用户的活跃会话
- 支持单点登录（一个用户只能有一个有效token）
- 支持会话延长（活跃用户自动延长）

### 4. 信息安全
- JWT中不包含敏感信息
- 即使JWT被解析，也只能看到用户ID

## API使用示例

### 登录
```javascript
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}

// 响应
{
  "code": 200,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIs..."
  },
  "message": "登录成功"
}
```

### 访问受保护的接口
```javascript
GET /api/auth/profile
Headers: {
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIs..."
}
```

### 登出
```javascript
POST /api/auth/logout
Headers: {
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIs..."
}
```

## 配置说明

### 环境变量
```env
# JWT配置
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=30d  # JWT过期时间（实际由Redis控制）

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=123456
REDIS_DB=2
```

### 过期时间配置
```javascript
// Redis中token的实际过期时间
const TOKEN_TTL = 18000; // 5小时 = 5 * 60 * 60

// 在登录/注册时设置
await redisUtils.setUserToken(userId, token, TOKEN_TTL);

// 在验证时刷新（活跃用户延长会话）
await redisUtils.refreshUserToken(userId, TOKEN_TTL);
```

## 监控和管理

### 查看token会话
```javascript
// 通过token获取用户信息
const tokenData = await redisUtils.getTokenData(token);

// 获取token剩余时间
const ttl = await redisUtils.getTokenTTL(token);

// 验证token并获取用户ID
const userId = await redisUtils.validateToken(token);
```

### 强制登出管理
```javascript
// 删除特定token（单设备登出）
await redisUtils.deleteToken(token);

// 删除用户的所有token（全局登出）
await redisUtils.deleteUserTokens(userId);
```

### 会话统计
```javascript
// 可以通过Redis的keys命令查看活跃会话
// keys token:*

// 统计活跃token数量
const activeTokens = await redisClient.getClient().keys('token:*');
console.log('活跃会话数:', activeTokens.length);
```

## 故障处理

### Redis连接失败
- JWT验证仍然有效（降级处理）
- 记录警告日志
- 不阻塞正常业务流程

### Token不一致
- JWT有效但Redis中不存在：要求重新登录
- JWT中用户ID与Redis中不一致：清除Redis记录并要求重新登录
- JWT无效：直接拒绝访问

## 性能优化

### 1. 连接池
- Redis使用连接池管理连接
- 避免频繁建立/断开连接

### 2. 批量操作
- 支持批量删除用户缓存
- 减少Redis操作次数

### 3. 过期策略
- Redis自动清理过期的token
- 无需手动清理过期数据

## 测试验证

运行测试脚本验证功能：
```bash
cd server
node test-new-token-structure.js
```

测试覆盖：
- JWT生成和解析
- Token作为键的Redis存储
- 通过token直接获取用户ID
- 多token并存和管理
- Token刷新和删除
- 过期时间控制
- 精确登出和全局登出

## 总结

这种JWT + Redis的混合认证机制结合了两者的优势：
- **JWT的无状态特性**: 减少服务器存储压力
- **Redis的集中控制**: 实现真正的会话管理
- **安全性提升**: 可撤销、实时控制、信息最小化
- **性能优化**: 活跃用户会话延长、自动过期清理

这是一个现代化、安全、高效的认证解决方案。
