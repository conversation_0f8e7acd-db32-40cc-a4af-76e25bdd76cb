const { createClient } = require('redis');
require('dotenv').config();

class RedisClient {
  constructor() {
    this.client = null;
    this.isConnected = false;
  }

  async connect() {
    try {
      // Redis配置
      const redisConfig = {
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD || undefined,
        db: process.env.REDIS_DB || 0,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true
      };

      // 创建Redis客户端
      this.client = createClient({
        socket: {
          host: redisConfig.host,
          port: redisConfig.port,
          reconnectStrategy: (retries) => {
            if (retries > 10) {
              console.error('Redis重连次数超过限制，停止重连');
              return false;
            }
            return Math.min(retries * 50, 500);
          }
        },
        password: redisConfig.password,
        database: redisConfig.db
      });

      // 监听连接事件
      this.client.on('connect', () => {
        console.log('Redis客户端正在连接...');
      });

      this.client.on('ready', () => {
        console.log('Redis连接已就绪');
        this.isConnected = true;
      });

      this.client.on('error', (err) => {
        console.error('Redis连接错误:', err.message);
        this.isConnected = false;
      });

      this.client.on('end', () => {
        console.log('Redis连接已断开');
        this.isConnected = false;
      });

      this.client.on('reconnecting', () => {
        console.log('Redis正在重连...');
      });

      // 连接到Redis
      await this.client.connect();
      
      // 测试连接
      await this.client.ping();
      console.log('Redis连接测试成功');
      
      return true;
    } catch (error) {
      console.error('Redis连接失败:', error.message);
      this.isConnected = false;
      return false;
    }
  }

  async disconnect() {
    try {
      if (this.client && this.isConnected) {
        await this.client.quit();
        console.log('Redis连接已关闭');
      }
    } catch (error) {
      console.error('Redis断开连接时出错:', error.message);
    }
  }

  // 获取客户端实例
  getClient() {
    return this.client;
  }

  // 检查连接状态
  isReady() {
    return this.isConnected && this.client && this.client.isReady;
  }

  // 测试连接
  async testConnection() {
    try {
      if (!this.isReady()) {
        return false;
      }
      await this.client.ping();
      return true;
    } catch (error) {
      console.error('Redis连接测试失败:', error.message);
      return false;
    }
  }
}

// 创建单例实例
const redisClient = new RedisClient();

module.exports = {
  redisClient,
  RedisClient
};
