{"name": "mailcode-server", "version": "1.0.0", "description": "MailCode后端服务", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "mailcode", "api"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.14.3", "redis": "^5.7.0", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.0.2"}}