const jwt = require('jsonwebtoken');
require('dotenv').config();

const JWT_SECRET = process.env.JWT_SECRET || 'default_secret';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

/**
 * 生成JWT token
 * @param {Object} payload - 要编码的数据
 * @returns {String} JWT token
 */
function generateToken(payload) {
  try {
    return jwt.sign(payload, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN,
      issuer: 'mailcode-api'
    });
  } catch (error) {
    console.error('生成token失败:', error.message);
    throw new Error('生成token失败');
  }
}

/**
 * 验证JWT token
 * @param {String} token - JWT token
 * @returns {Object} 解码后的数据
 */
function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Token已过期');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Token无效');
    } else {
      throw new Error('Token验证失败');
    }
  }
}

/**
 * 从请求头中提取token
 * @param {Object} req - Express请求对象
 * @returns {String|null} token或null
 */
function extractTokenFromHeader(req) {
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7); // 移除 'Bearer ' 前缀
  }
  return null;
}

/**
 * 解码token（不验证签名，用于获取过期token的信息）
 * @param {String} token - JWT token
 * @returns {Object} 解码后的数据
 */
function decodeToken(token) {
  try {
    return jwt.decode(token);
  } catch (error) {
    throw new Error('Token解码失败');
  }
}

module.exports = {
  generateToken,
  verifyToken,
  extractTokenFromHeader,
  decodeToken
};
