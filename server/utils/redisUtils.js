const { redisClient } = require('../config/redis');

class RedisUtils {
  constructor() {
    this.client = redisClient;
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {number} ttl - 过期时间（秒），默认1小时
   */
  async set(key, value, ttl = 3600) {
    try {
      if (!this.client.isReady()) {
        console.warn('Redis未连接，跳过缓存设置');
        return false;
      }

      const serializedValue = JSON.stringify(value);
      
      if (ttl > 0) {
        await this.client.getClient().setEx(key, ttl, serializedValue);
      } else {
        await this.client.getClient().set(key, serializedValue);
      }
      
      return true;
    } catch (error) {
      console.error('Redis设置缓存失败:', error.message);
      return false;
    }
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   */
  async get(key) {
    try {
      if (!this.client.isReady()) {
        console.warn('Redis未连接，跳过缓存获取');
        return null;
      }

      const value = await this.client.getClient().get(key);
      
      if (value === null) {
        return null;
      }

      return JSON.parse(value);
    } catch (error) {
      console.error('Redis获取缓存失败:', error.message);
      return null;
    }
  }

  /**
   * 删除缓存
   * @param {string} key - 缓存键
   */
  async del(key) {
    try {
      if (!this.client.isReady()) {
        console.warn('Redis未连接，跳过缓存删除');
        return false;
      }

      const result = await this.client.getClient().del(key);
      return result > 0;
    } catch (error) {
      console.error('Redis删除缓存失败:', error.message);
      return false;
    }
  }

  /**
   * 检查键是否存在
   * @param {string} key - 缓存键
   */
  async exists(key) {
    try {
      if (!this.client.isReady()) {
        return false;
      }

      const result = await this.client.getClient().exists(key);
      return result === 1;
    } catch (error) {
      console.error('Redis检查键存在失败:', error.message);
      return false;
    }
  }

  /**
   * 设置键的过期时间
   * @param {string} key - 缓存键
   * @param {number} ttl - 过期时间（秒）
   */
  async expire(key, ttl) {
    try {
      if (!this.client.isReady()) {
        return false;
      }

      const result = await this.client.getClient().expire(key, ttl);
      return result === 1;
    } catch (error) {
      console.error('Redis设置过期时间失败:', error.message);
      return false;
    }
  }

  /**
   * 获取键的剩余过期时间
   * @param {string} key - 缓存键
   */
  async ttl(key) {
    try {
      if (!this.client.isReady()) {
        return -1;
      }

      return await this.client.getClient().ttl(key);
    } catch (error) {
      console.error('Redis获取TTL失败:', error.message);
      return -1;
    }
  }

  /**
   * 批量删除匹配模式的键
   * @param {string} pattern - 匹配模式
   */
  async deletePattern(pattern) {
    try {
      if (!this.client.isReady()) {
        return 0;
      }

      const keys = await this.client.getClient().keys(pattern);
      if (keys.length === 0) {
        return 0;
      }

      const result = await this.client.getClient().del(keys);
      return result;
    } catch (error) {
      console.error('Redis批量删除失败:', error.message);
      return 0;
    }
  }

  /**
   * 增加计数器
   * @param {string} key - 计数器键
   * @param {number} increment - 增加值，默认1
   */
  async incr(key, increment = 1) {
    try {
      if (!this.client.isReady()) {
        return 0;
      }

      if (increment === 1) {
        return await this.client.getClient().incr(key);
      } else {
        return await this.client.getClient().incrBy(key, increment);
      }
    } catch (error) {
      console.error('Redis计数器增加失败:', error.message);
      return 0;
    }
  }

  /**
   * 减少计数器
   * @param {string} key - 计数器键
   * @param {number} decrement - 减少值，默认1
   */
  async decr(key, decrement = 1) {
    try {
      if (!this.client.isReady()) {
        return 0;
      }

      if (decrement === 1) {
        return await this.client.getClient().decr(key);
      } else {
        return await this.client.getClient().decrBy(key, decrement);
      }
    } catch (error) {
      console.error('Redis计数器减少失败:', error.message);
      return 0;
    }
  }

  /**
   * 哈希表操作 - 设置字段
   * @param {string} key - 哈希表键
   * @param {string} field - 字段名
   * @param {any} value - 字段值
   */
  async hSet(key, field, value) {
    try {
      if (!this.client.isReady()) {
        return false;
      }

      const serializedValue = JSON.stringify(value);
      await this.client.getClient().hSet(key, field, serializedValue);
      return true;
    } catch (error) {
      console.error('Redis哈希表设置失败:', error.message);
      return false;
    }
  }

  /**
   * 哈希表操作 - 获取字段
   * @param {string} key - 哈希表键
   * @param {string} field - 字段名
   */
  async hGet(key, field) {
    try {
      if (!this.client.isReady()) {
        return null;
      }

      const value = await this.client.getClient().hGet(key, field);
      
      if (value === null) {
        return null;
      }

      return JSON.parse(value);
    } catch (error) {
      console.error('Redis哈希表获取失败:', error.message);
      return null;
    }
  }

  /**
   * 哈希表操作 - 删除字段
   * @param {string} key - 哈希表键
   * @param {string} field - 字段名
   */
  async hDel(key, field) {
    try {
      if (!this.client.isReady()) {
        return false;
      }

      const result = await this.client.getClient().hDel(key, field);
      return result > 0;
    } catch (error) {
      console.error('Redis哈希表删除失败:', error.message);
      return false;
    }
  }
}

// 创建单例实例
const redisUtils = new RedisUtils();

module.exports = redisUtils;
