const { query, transaction } = require('../config/database');
const { hashPassword, comparePassword } = require('../utils/bcrypt');

class User {

  // 创建用户
  static async create(userData) {
    const { username, email, password, mp_open_id } = userData;
    // 生成UUID作为用户ID
    const { v4: uuidv4 } = require('uuid');
    const id = uuidv4();

    // 加密密码
    const hashedPassword = await hashPassword(password);

    const sql = 'INSERT INTO user (id, username, email, password, mp_open_id) VALUES (?, ?, ?, ?, ?)';

    try {
      await query(sql, [id, username, email, hashedPassword, mp_open_id]);
      return {
        id,
        username,
        email,
        mp_open_id,
        create_time: new Date()
      };
    } catch (error) {
      console.error('创建用户失败:', error.message);
      throw error;
    }
  }

  // 根据ID查找用户
  static async findById(id) {
    const sql = 'SELECT id, username, email, mp_open_id, create_time, update_time FROM user WHERE id = ?';

    try {
      const users = await query(sql, [id]);
      return users[0] || null;
    } catch (error) {
      console.error('查找用户失败:', error.message);
      throw error;
    }
  }

  // 根据邮箱查找用户
  static async findByEmail(email) {
    const sql = 'SELECT id, username, email, password, mp_open_id, create_time, update_time FROM user WHERE email = ?';

    try {
      const users = await query(sql, [email]);
      return users[0] || null;
    } catch (error) {
      console.error('查找用户失败:', error.message);
      throw error;
    }
  }

  // 获取所有用户
  static async findAll(page = 1, pageSize = 10) {
    const offset = (page - 1) * pageSize;
    const sql = 'SELECT id, username, email, mp_open_id, create_time, update_time FROM user LIMIT ? OFFSET ?';

    try {
      const users = await query(sql, [pageSize, offset]);
      return users;
    } catch (error) {
      console.error('获取用户列表失败:', error.message);
      throw error;
    }
  }

  // 更新用户
  static async update(id, userData) {
    const { username, email, mp_open_id } = userData;
    const sql = 'UPDATE user SET username = ?, email = ?, mp_open_id = ? WHERE id = ?';

    try {
      const result = await query(sql, [username, email, mp_open_id, id]);
      if (result.affectedRows === 0) {
        return null;
      }
      return await this.findById(id);
    } catch (error) {
      console.error('更新用户失败:', error.message);
      throw error;
    }
  }

  // 删除用户
  static async delete(id) {
    const sql = 'DELETE FROM user WHERE id = ?';

    try {
      const result = await query(sql, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除用户失败:', error.message);
      throw error;
    }
  }

  // 验证用户登录
  static async validateLogin(email, password) {
    try {
      // 查找用户（包含密码）
      const user = await this.findByEmail(email);
      if (!user) {
        return null;
      }

      // 验证密码
      const isPasswordValid = await comparePassword(password, user.password);
      if (!isPasswordValid) {
        return null;
      }

      // 返回用户信息（不包含密码）
      const { password: _, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      console.error('用户登录验证失败:', error.message);
      throw error;
    }
  }

  // 更新密码
  static async updatePassword(id, newPassword) {
    try {
      const hashedPassword = await hashPassword(newPassword);
      const sql = 'UPDATE user SET password = ? WHERE id = ?';
      const result = await query(sql, [hashedPassword, id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新密码失败:', error.message);
      throw error;
    }
  }
}

module.exports = User;
